// --- ASPECT RATIO CALCULATOR LOGIC ---
(function() {
    // Initialize the aspect ratio calculator when loaded
    initializeAspectRatioCalculator();

    function initializeAspectRatioCalculator() {
        // Get elements for the preset calculator
        const select = document.getElementById('aspect-ratio-select');
        const presetWidthInput = document.getElementById('preset-width');
        const presetHeightInput = document.getElementById('preset-height');

        // Get elements for the custom ratio finder
        const customWidthInput = document.getElementById('custom-width');
        const customHeightInput = document.getElementById('custom-height');
        const calculateBtn = document.getElementById('calculate-custom-ratio');
        const resultBox = document.getElementById('custom-ratio-result');

        // Add event listeners for the preset calculator for real-time updates
        select?.addEventListener('change', () => handlePresetCalculation('width'));
        presetWidthInput?.addEventListener('input', () => handlePresetCalculation('width'));
        presetHeightInput?.addEventListener('input', () => handlePresetCalculation('height'));

        // Add event listener for the custom calculator button
        calculateBtn?.addEventListener('click', calculateCustomRatio);

        // --- Calculation Functions ---

        function handlePresetCalculation(changedInput) {
            const ratio = select.value.split('/').map(Number); // e.g., [16, 9]
            const width = parseFloat(presetWidthInput.value);
            const height = parseFloat(presetHeightInput.value);

            if (changedInput === 'width' && width > 0) {
                const newHeight = (width / ratio[0]) * ratio[1];
                presetHeightInput.value = Math.round(newHeight);
            } else if (changedInput === 'height' && height > 0) {
                const newWidth = (height / ratio[1]) * ratio[0];
                presetWidthInput.value = Math.round(newWidth);
            }
        }

        function calculateCustomRatio() {
            const width = parseInt(customWidthInput.value);
            const height = parseInt(customHeightInput.value);

            if (!width || !height || width <= 0 || height <= 0) {
                resultBox.textContent = "Please enter valid width and height.";
                resultBox.classList.remove('success');
                return;
            }

            const divisor = gcd(width, height);
            const simplifiedWidth = width / divisor;
            const simplifiedHeight = height / divisor;
            const decimalRatio = (width / height).toFixed(3);

            resultBox.innerHTML = `Simplified: <strong>${simplifiedWidth}:${simplifiedHeight}</strong> (Decimal: ${decimalRatio})`;
            resultBox.classList.add('success');
        }

        // Helper function to find the Greatest Common Divisor (GCD) using Euclidean algorithm
        function gcd(a, b) {
            while (b) {
                [a, b] = [b, a % b];
            }
            return a;
        }
    }
})();
