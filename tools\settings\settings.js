// Settings Tool JavaScript
(function() {
    // Initialize settings when loaded
    initializeSettings();

    function initializeSettings() {
        // Load saved settings
        loadSettings();
        
        // Set up event listeners
        setupEventListeners();
        
        console.log('Settings tool loaded');
    }

    function setupEventListeners() {
        // Theme selector
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', handleThemeChange);
        }

        // Auto-save settings checkbox
        const autoSaveCheckbox = document.getElementById('auto-save-settings');
        if (autoSaveCheckbox) {
            autoSaveCheckbox.addEventListener('change', handleAutoSaveChange);
        }

        // Show tooltips checkbox
        const tooltipsCheckbox = document.getElementById('show-tooltips');
        if (tooltipsCheckbox) {
            tooltipsCheckbox.addEventListener('change', handleTooltipsChange);
        }

        // Reset settings button
        const resetButton = document.getElementById('reset-settings');
        if (resetButton) {
            resetButton.addEventListener('click', handleResetSettings);
        }
    }

    function handleThemeChange(event) {
        const selectedTheme = event.target.value;
        applyTheme(selectedTheme);
        saveSettings();
        
        // Show feedback
        showSettingsFeedback('Theme changed successfully!');
    }

    function applyTheme(theme) {
        // Apply theme to document root
        if (theme === 'dark') {
            document.documentElement.removeAttribute('data-theme');
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
    }

    function handleAutoSaveChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Auto-save ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleTooltipsChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Tooltips ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleResetSettings() {
        if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {
            // Reset to defaults
            const defaults = {
                theme: 'dark',
                autoSave: false,
                showTooltips: true
            };
            
            // Apply defaults to UI
            document.getElementById('theme-select').value = defaults.theme;
            document.getElementById('auto-save-settings').checked = defaults.autoSave;
            document.getElementById('show-tooltips').checked = defaults.showTooltips;
            
            // Apply theme
            applyTheme(defaults.theme);
            
            // Save defaults
            localStorage.setItem('comfyui-helper-settings', JSON.stringify(defaults));
            
            showSettingsFeedback('Settings reset to defaults');
        }
    }

    function saveSettings() {
        const settings = {
            theme: document.getElementById('theme-select')?.value || 'dark',
            autoSave: document.getElementById('auto-save-settings')?.checked || false,
            showTooltips: document.getElementById('show-tooltips')?.checked || true
        };
        
        localStorage.setItem('comfyui-helper-settings', JSON.stringify(settings));
    }

    function loadSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                
                // Apply saved theme
                if (settings.theme) {
                    const themeSelect = document.getElementById('theme-select');
                    if (themeSelect) {
                        themeSelect.value = settings.theme;
                        applyTheme(settings.theme);
                    }
                }
                
                // Apply other settings
                const autoSaveCheckbox = document.getElementById('auto-save-settings');
                if (autoSaveCheckbox && typeof settings.autoSave === 'boolean') {
                    autoSaveCheckbox.checked = settings.autoSave;
                }
                
                const tooltipsCheckbox = document.getElementById('show-tooltips');
                if (tooltipsCheckbox && typeof settings.showTooltips === 'boolean') {
                    tooltipsCheckbox.checked = settings.showTooltips;
                }
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
    }

    function showSettingsFeedback(message) {
        // Create or update feedback element
        let feedback = document.querySelector('.settings-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'settings-feedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--success-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }
        
        feedback.textContent = message;
        
        // Show feedback
        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
            feedback.style.transform = 'translateY(0)';
        });
        
        // Hide feedback after 3 seconds
        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    // Initialize theme on page load (for when settings tool isn't the first loaded)
    function initializeThemeOnLoad() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                if (settings.theme) {
                    applyTheme(settings.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to initialize theme:', error);
        }
    }
    
    // Call theme initialization immediately
    initializeThemeOnLoad();
})();
