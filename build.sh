#!/bin/bash

echo ""
echo "========================================"
echo "  Comfyui Helper Tools - Build System"
echo "========================================"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Run the build script
echo "🔨 Running build script..."
node build.js

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build completed successfully!"
    echo "You can now open index.html in your browser."
else
    echo ""
    echo "❌ Build failed!"
    exit 1
fi

echo ""
