@echo off
echo.
echo ========================================
echo   Comfyui Helper Tools - Build System
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ? Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Run the build script
echo ?? Running build script...
node build.js

if %errorlevel% equ 0 (
    echo.
    echo ? Build completed successfully!
    echo You can now open index.html in your browser.
) else (
    echo.
    echo ? Build failed!
)

echo.
pause
