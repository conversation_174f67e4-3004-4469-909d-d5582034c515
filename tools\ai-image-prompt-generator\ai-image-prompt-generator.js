function initializeAIImagePromptGenerator() {
    // Initialize tab functionality
    initializeTabs();
    
    // Initialize Image to Prompt feature
    initializeImageToPrompt();
    
    // Initialize Magic Enhance feature
    initializeMagicEnhance();
    
    // Initialize AI Describe feature
    initializeAIDescribe();
    
    console.log('AI Image Prompt Generator tool loaded');
}

// Tab Management
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// Image to Prompt Feature
function initializeImageToPrompt() {
    const uploadArea = document.getElementById('image-upload-area');
    const fileInput = document.getElementById('image-file-input');
    const uploadedImage = document.getElementById('uploaded-image');
    const previewImage = document.getElementById('preview-image');
    const removeImageBtn = document.getElementById('remove-image');
    const generateBtn = document.getElementById('generate-prompt-btn');
    const promptResult = document.getElementById('prompt-result');
    const generatedPrompt = document.getElementById('generated-prompt');
    const copyPromptBtn = document.getElementById('copy-prompt-btn');
    
    let currentImageFile = null;
    
    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        if (!currentImageFile) {
            fileInput.click();
        }
    });
    
    // File input change handler
    fileInput.addEventListener('change', handleImageUpload);
    
    // Drag and drop handlers
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleImageDrop);
    
    // Remove image handler
    removeImageBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        resetImageUpload();
    });
    
    // Generate prompt handler
    generateBtn.addEventListener('click', generatePromptFromImage);
    
    // Copy prompt handler
    copyPromptBtn.addEventListener('click', () => copyToClipboard(generatedPrompt.value, copyPromptBtn));
    
    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file && isValidImageFile(file)) {
            displayUploadedImage(file);
        }
    }
    
    function handleDragOver(event) {
        event.preventDefault();
        uploadArea.classList.add('dragover');
    }
    
    function handleDragLeave(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
    }
    
    function handleImageDrop(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0 && isValidImageFile(files[0])) {
            displayUploadedImage(files[0]);
        }
    }
    
    function displayUploadedImage(file) {
        currentImageFile = file;
        const reader = new FileReader();
        
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            uploadedImage.style.display = 'block';
            uploadArea.querySelector('.upload-placeholder').style.display = 'none';
            generateBtn.disabled = false;
        };
        
        reader.readAsDataURL(file);
    }
    
    function resetImageUpload() {
        currentImageFile = null;
        uploadedImage.style.display = 'none';
        uploadArea.querySelector('.upload-placeholder').style.display = 'block';
        generateBtn.disabled = true;
        promptResult.style.display = 'none';
        fileInput.value = '';
    }
    
    async function generatePromptFromImage() {
        if (!currentImageFile) return;
        
        const promptModel = document.getElementById('prompt-model-select').value;
        
        try {
            setButtonLoading(generateBtn, true);
            
            // Convert image to base64
            const base64Image = await fileToBase64(currentImageFile);
            
            // Get AI response
            const prompt = await callAIForImageToPrompt(base64Image, promptModel);
            
            // Display result
            generatedPrompt.value = prompt;
            promptResult.style.display = 'block';
            
        } catch (error) {
            console.error('Error generating prompt:', error);
            showError('Failed to generate prompt. Please check your AI provider settings.');
        } finally {
            setButtonLoading(generateBtn, false);
        }
    }
}

// Magic Enhance Feature
function initializeMagicEnhance() {
    const simpleText = document.getElementById('simple-text');
    const enhanceBtn = document.getElementById('enhance-text-btn');
    const enhancedResult = document.getElementById('enhanced-result');
    const enhancedPrompt = document.getElementById('enhanced-prompt');
    const copyEnhancedBtn = document.getElementById('copy-enhanced-btn');
    
    enhanceBtn.addEventListener('click', enhanceText);
    copyEnhancedBtn.addEventListener('click', () => copyToClipboard(enhancedPrompt.value, copyEnhancedBtn));
    
    async function enhanceText() {
        const text = simpleText.value.trim();
        if (!text) {
            showError('Please enter some text to enhance.');
            return;
        }
        
        try {
            setButtonLoading(enhanceBtn, true);
            
            const enhancedText = await callAIForTextEnhancement(text);
            
            enhancedPrompt.value = enhancedText;
            enhancedResult.style.display = 'block';
            
        } catch (error) {
            console.error('Error enhancing text:', error);
            showError('Failed to enhance text. Please check your AI provider settings.');
        } finally {
            setButtonLoading(enhanceBtn, false);
        }
    }
}

// AI Describe Feature
function initializeAIDescribe() {
    const uploadArea = document.getElementById('describe-upload-area');
    const fileInput = document.getElementById('describe-file-input');
    const uploadedImage = document.getElementById('describe-uploaded-image');
    const previewImage = document.getElementById('describe-preview-image');
    const removeImageBtn = document.getElementById('describe-remove-image');
    const descriptionOption = document.getElementById('description-option');
    const customQuestionGroup = document.getElementById('custom-question-group');
    const customQuestion = document.getElementById('custom-question');
    const analyzeBtn = document.getElementById('analyze-image-btn');
    const analysisResult = document.getElementById('analysis-result');
    const analysisText = document.getElementById('analysis-text');
    const copyAnalysisBtn = document.getElementById('copy-analysis-btn');
    
    let currentDescribeImageFile = null;
    
    // Description option change handler
    descriptionOption.addEventListener('change', () => {
        if (descriptionOption.value === 'custom-question') {
            customQuestionGroup.style.display = 'block';
        } else {
            customQuestionGroup.style.display = 'none';
        }
    });
    
    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        if (!currentDescribeImageFile) {
            fileInput.click();
        }
    });
    
    // File input change handler
    fileInput.addEventListener('change', handleDescribeImageUpload);
    
    // Drag and drop handlers
    uploadArea.addEventListener('dragover', handleDescribeDragOver);
    uploadArea.addEventListener('dragleave', handleDescribeDragLeave);
    uploadArea.addEventListener('drop', handleDescribeImageDrop);
    
    // Remove image handler
    removeImageBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        resetDescribeImageUpload();
    });
    
    // Analyze image handler
    analyzeBtn.addEventListener('click', analyzeImage);
    
    // Copy analysis handler
    copyAnalysisBtn.addEventListener('click', () => copyToClipboard(analysisText.value, copyAnalysisBtn));
    
    function handleDescribeImageUpload(event) {
        const file = event.target.files[0];
        if (file && isValidImageFile(file)) {
            displayDescribeUploadedImage(file);
        }
    }
    
    function handleDescribeDragOver(event) {
        event.preventDefault();
        uploadArea.classList.add('dragover');
    }
    
    function handleDescribeDragLeave(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
    }
    
    function handleDescribeImageDrop(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0 && isValidImageFile(files[0])) {
            displayDescribeUploadedImage(files[0]);
        }
    }
    
    function displayDescribeUploadedImage(file) {
        currentDescribeImageFile = file;
        const reader = new FileReader();
        
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            uploadedImage.style.display = 'block';
            uploadArea.querySelector('.upload-placeholder').style.display = 'none';
            analyzeBtn.disabled = false;
        };
        
        reader.readAsDataURL(file);
    }
    
    function resetDescribeImageUpload() {
        currentDescribeImageFile = null;
        uploadedImage.style.display = 'none';
        uploadArea.querySelector('.upload-placeholder').style.display = 'block';
        analyzeBtn.disabled = true;
        analysisResult.style.display = 'none';
        fileInput.value = '';
    }
    
    async function analyzeImage() {
        if (!currentDescribeImageFile) return;
        
        const option = descriptionOption.value;
        const question = customQuestion.value.trim();
        
        if (option === 'custom-question' && !question) {
            showError('Please enter a custom question.');
            return;
        }
        
        try {
            setButtonLoading(analyzeBtn, true);
            
            // Convert image to base64
            const base64Image = await fileToBase64(currentDescribeImageFile);
            
            // Get AI response
            const analysis = await callAIForImageAnalysis(base64Image, option, question);
            
            // Display result
            analysisText.value = analysis;
            analysisResult.style.display = 'block';
            
        } catch (error) {
            console.error('Error analyzing image:', error);
            showError('Failed to analyze image. Please check your AI provider settings.');
        } finally {
            setButtonLoading(analyzeBtn, false);
        }
    }
}

// Utility Functions
function isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    return validTypes.includes(file.type);
}

function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(() => {
        const originalText = button.textContent;
        button.textContent = '✓ Copied!';
        button.style.backgroundColor = 'var(--success-color)';

        setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = '';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        showError('Failed to copy to clipboard');
    });
}

function showError(message) {
    // Create or update error notification
    let errorNotification = document.querySelector('.error-notification');
    if (!errorNotification) {
        errorNotification = document.createElement('div');
        errorNotification.className = 'error-notification';
        errorNotification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--error-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            max-width: 400px;
        `;
        document.body.appendChild(errorNotification);
    }

    errorNotification.textContent = message;

    // Show notification
    requestAnimationFrame(() => {
        errorNotification.style.opacity = '1';
        errorNotification.style.transform = 'translateY(0)';
    });

    // Hide notification after 5 seconds
    setTimeout(() => {
        errorNotification.style.opacity = '0';
        errorNotification.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            if (errorNotification.parentNode) {
                errorNotification.parentNode.removeChild(errorNotification);
            }
        }, 300);
    }, 5000);
}

// AI API Functions (placeholder implementations)
async function callAIForImageToPrompt(base64Image, promptModel) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

    const prompts = {
        'general': 'A detailed natural language description of the image showing [placeholder - AI provider not configured]',
        'flux': 'Concise, natural language prompt optimized for Flux: [placeholder - AI provider not configured]',
        'stable-diffusion': 'Structured prompt with tags for Stable Diffusion: [placeholder - AI provider not configured]'
    };

    return prompts[promptModel] || prompts['general'];
}

async function callAIForTextEnhancement(text) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call

    return `Enhanced version of "${text}": [placeholder - AI provider not configured. Please configure your AI provider in Settings to use this feature.]`;
}

async function callAIForImageAnalysis(base64Image, option, customQuestion) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

    const responses = {
        'describe-detail': 'Detailed image description: [placeholder - AI provider not configured]',
        'describe-brief': 'Brief image description: [placeholder - AI provider not configured]',
        'describe-person': 'Person description: [placeholder - AI provider not configured]',
        'recognize-objects': 'Objects recognized: [placeholder - AI provider not configured]',
        'analyze-art-style': 'Art style analysis: [placeholder - AI provider not configured]',
        'extract-text': 'Extracted text: [placeholder - AI provider not configured]',
        'general-prompt': 'General image prompt: [placeholder - AI provider not configured]',
        'flux-prompt': 'Flux prompt: [placeholder - AI provider not configured]',
        'stable-diffusion-prompt': 'Stable Diffusion prompt: [placeholder - AI provider not configured]',
        'custom-question': `Answer to "${customQuestion}": [placeholder - AI provider not configured]`
    };

    return responses[option] || 'Analysis result: [placeholder - AI provider not configured]';
}
