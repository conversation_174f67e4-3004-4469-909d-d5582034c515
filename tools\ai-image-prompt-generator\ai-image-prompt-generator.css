/* --- AI Image Prompt Generator Styles --- */

/* Feature Tabs */
.feature-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--subtle-text-color);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-family: 'Poppins', sans-serif;
    font-size: 0.95rem;
    font-weight: 500;
}

.tab-button:hover {
    color: var(--text-color);
    background-color: rgba(122, 162, 247, 0.05);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-icon {
    width: 20px;
    height: 20px;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Tool Section */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 0.5rem;
    color: #fff;
    font-size: 1.2rem;
}

.section-description {
    color: var(--subtle-text-color);
    margin-bottom: 2rem;
    line-height: 1.5;
}

/* Upload Area */
.upload-area {
    position: relative;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(122, 162, 247, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(122, 162, 247, 0.1);
}

.upload-placeholder {
    pointer-events: none;
}

.upload-icon {
    color: var(--subtle-text-color);
    margin-bottom: 1rem;
}

.upload-area p {
    margin: 0.5rem 0;
    color: var(--text-color);
}

.upload-hint {
    font-size: 0.9rem;
    color: var(--subtle-text-color) !important;
}

/* Uploaded Image */
.uploaded-image {
    position: relative;
    display: inline-block;
}

.uploaded-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--error-color);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.remove-image:hover {
    background-color: #d32f2f;
    transform: scale(1.1);
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.form-group textarea::placeholder {
    color: var(--subtle-text-color);
}

/* Result Area */
.result-area {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.result-area label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.result-area textarea {
    width: 100%;
    min-height: 150px;
    padding: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    resize: vertical;
    line-height: 1.5;
}

.result-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
}

/* Action Buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
}

.action-button:hover:not(:disabled) {
    background-color: #5a7bd8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(122, 162, 247, 0.3);
}

.action-button:disabled {
    background-color: var(--subtle-text-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.action-button.secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-button svg {
    width: 16px;
    height: 16px;
}

/* Loading State */
.action-button.loading {
    position: relative;
    color: transparent;
}

.action-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-tabs {
        flex-direction: column;
        gap: 0;
    }
    
    .tab-button {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }
    
    .tab-button.active {
        border-bottom-color: var(--primary-color);
    }
    
    .tool-section {
        padding: 1.5rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .action-button {
        justify-content: center;
    }
}
