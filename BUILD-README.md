# Comfyui Helper Tools - Build System

This project uses a modular build system that allows you to edit individual tool files while maintaining compatibility with direct file access (no server required).

## 🏗️ How It Works

1. **Edit individual tool files** in the `tools/` directory
2. **Run the build script** to automatically embed them into `script.js`
3. **Open `index.html`** directly in your browser - no server needed!

## 📁 Project Structure

```
Comfyui-tool-helper/
├── index.html              # Main dashboard
├── style.css               # Global styles and themes
├── script.js               # Generated file (don't edit directly!)
├── script-template.js      # Template for script.js
├── build.js                # Build script
├── build.bat               # Windows build script
├── build.sh                # Unix/Linux/Mac build script
└── tools/                  # Individual tool modules
    ├── prompt-generator/
    │   ├── prompt-generator.html
    │   ├── prompt-generator.css
    │   └── prompt-generator.js
    ├── aspect-ratio-calculator/
    │   ├── aspect-ratio-calculator.html
    │   ├── aspect-ratio-calculator.css
    │   └── aspect-ratio-calculator.js
    └── settings/
        ├── settings.html
        ├── settings.css
        └── settings.js
```

## 🚀 Quick Start

### Windows
```bash
# Double-click build.bat or run in command prompt:
build.bat
```

### Unix/Linux/Mac
```bash
# Make executable (first time only):
chmod +x build.sh

# Run build:
./build.sh
```

### Manual (any platform)
```bash
node build.js
```

## ✏️ Development Workflow

1. **Edit tool files** in `tools/[tool-name]/` directory:
   - `[tool-name].html` - Tool's HTML template
   - `[tool-name].css` - Tool's styles
   - `[tool-name].js` - Tool's JavaScript functionality

2. **Run build script** after making changes:
   ```bash
   # Windows
   build.bat
   
   # Unix/Linux/Mac
   ./build.sh
   ```

3. **Test your changes** by opening `index.html` in your browser

## 🔧 Adding New Tools

1. **Create new tool directory**:
   ```
   tools/my-new-tool/
   ├── my-new-tool.html
   ├── my-new-tool.css
   └── my-new-tool.js
   ```

2. **Add navigation item** to `index.html`:
   ```html
   <li class="nav-item">
       <a href="#" data-target="my-new-tool">
           <svg class="nav-icon"><!-- icon --></svg>
           <span class="nav-text">My New Tool</span>
       </a>
   </li>
   ```

3. **Run build script** to embed the new tool

4. **Test** by opening `index.html`

## 📝 Tool File Templates

### HTML Template (`[tool-name].html`)
```html
<div class="dashboard-view active">
    <h2>My Tool</h2>
    <p class="view-description">Tool description here.</p>
    
    <!-- Your tool content here -->
    
</div>
```

### CSS Template (`[tool-name].css`)
```css
/* Tool-specific styles */
.my-tool-class {
    /* styles */
}

/* Responsive design */
@media (max-width: 768px) {
    .my-tool-class {
        /* mobile styles */
    }
}
```

### JavaScript Template (`[tool-name].js`)
```javascript
// Tool initialization (wrapped in IIFE)
(function() {
    // Initialize when loaded
    initializeMyTool();

    function initializeMyTool() {
        // Your initialization code here
        const button = document.getElementById('my-button');
        if (button) {
            button.addEventListener('click', handleClick);
        }
    }

    function handleClick() {
        // Your event handlers here
    }
})();
```

## ⚠️ Important Notes

- **Don't edit `script.js` directly** - it's auto-generated!
- **Always run the build script** after editing tool files
- **Node.js is required** for the build system
- **The generated `script.js`** works without a server
- **Individual tool files** are preserved for development

## 🎨 Themes

The dashboard supports 5 themes:
- Dark (default)
- Light
- Ocean
- Forest
- Purple

Themes are configured in the main `style.css` file and managed through the Settings tool.

## 🔄 Build Process

The build script:
1. Scans the `tools/` directory for tool folders
2. Reads each tool's HTML, CSS, and JS files
3. Escapes content for safe embedding in template literals
4. Generates the `TOOL_TEMPLATES` object
5. Injects it into `script-template.js`
6. Outputs the final `script.js`

This ensures your individual tool files remain editable while the final output works without a server!
