<div class="dashboard-view active">
    <h2>AI Image Prompt Generation</h2>
    <p class="view-description">Generate, enhance, and analyze image prompts using AI. Upload images to create prompts or enhance your existing text.</p>

    <!-- Feature Tabs -->
    <div class="feature-tabs">
        <button class="tab-button active" data-tab="image-to-prompt">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
            </svg>
            Image to Prompt
        </button>
        <button class="tab-button" data-tab="magic-enhance">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Magic Enhance
        </button>
        <button class="tab-button" data-tab="ai-describe">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 11H5a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h4m6-6h4a2 2 0 0 1 2 2v3c0 1.1-.9 2-2 2h-4m-6 0V9a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2z"/>
            </svg>
            AI Describe
        </button>
    </div>

    <!-- Image to Prompt Tab -->
    <div class="tab-content active" id="image-to-prompt">
        <div class="tool-section">
            <h3>Convert Image to Prompt</h3>
            <p class="section-description">Upload an image and select the prompt model to generate optimized prompts for AI image generation.</p>
            
            <div class="upload-area" id="image-upload-area">
                <div class="upload-placeholder">
                    <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    <p>Click to upload or drag and drop an image</p>
                    <p class="upload-hint">Supports JPG, PNG, WebP, GIF</p>
                </div>
                <input type="file" id="image-file-input" accept="image/*" hidden>
                <div class="uploaded-image" id="uploaded-image" style="display: none;">
                    <img id="preview-image" src="" alt="Uploaded image">
                    <button class="remove-image" id="remove-image">×</button>
                </div>
            </div>

            <div class="form-group">
                <label for="prompt-model-select">Prompt Model</label>
                <select id="prompt-model-select">
                    <option value="general">General Image Prompt - Natural language description</option>
                    <option value="flux">Flux - Optimized for Flux AI models, concise natural language</option>
                    <option value="stable-diffusion">Stable Diffusion - Formatted for Stable Diffusion models</option>
                </select>
            </div>

            <button id="generate-prompt-btn" class="action-button" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Generate Prompt
            </button>

            <div class="result-area" id="prompt-result" style="display: none;">
                <label for="generated-prompt">Generated Prompt</label>
                <textarea id="generated-prompt" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-prompt-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Magic Enhance Tab -->
    <div class="tab-content" id="magic-enhance">
        <div class="tool-section">
            <h3>Magic Enhance</h3>
            <p class="section-description">Transform simple text into detailed, descriptive image prompts perfect for AI generation.</p>
            
            <div class="form-group">
                <label for="simple-text">Simple Text</label>
                <textarea id="simple-text" placeholder="Enter simple text like 'a cat in a garden' or 'futuristic city'..."></textarea>
            </div>

            <button id="enhance-text-btn" class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Enhance Text
            </button>

            <div class="result-area" id="enhanced-result" style="display: none;">
                <label for="enhanced-prompt">Enhanced Prompt</label>
                <textarea id="enhanced-prompt" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-enhanced-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Describe Tab -->
    <div class="tab-content" id="ai-describe">
        <div class="tool-section">
            <h3>AI Describe</h3>
            <p class="section-description">Let AI analyze and understand your images in detail. Get descriptions, recognize objects, or ask custom questions.</p>
            
            <div class="upload-area" id="describe-upload-area">
                <div class="upload-placeholder">
                    <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    <p>Click to upload or drag and drop an image</p>
                    <p class="upload-hint">Supports JPG, PNG, WebP, GIF</p>
                </div>
                <input type="file" id="describe-file-input" accept="image/*" hidden>
                <div class="uploaded-image" id="describe-uploaded-image" style="display: none;">
                    <img id="describe-preview-image" src="" alt="Uploaded image">
                    <button class="remove-image" id="describe-remove-image">×</button>
                </div>
            </div>

            <div class="form-group">
                <label for="description-option">Description Option</label>
                <select id="description-option">
                    <option value="describe-detail">Describe Image In Detail</option>
                    <option value="describe-brief">Describe Image Briefly</option>
                    <option value="describe-person">Describe The Person</option>
                    <option value="recognize-objects">Recognize Objects</option>
                    <option value="analyze-art-style">Analyze Art Style</option>
                    <option value="extract-text">Extract Text From Image</option>
                    <option value="general-prompt">General Image Prompt</option>
                    <option value="flux-prompt">Flux Prompt</option>
                    <option value="stable-diffusion-prompt">Stable Diffusion Prompt</option>
                    <option value="custom-question">Custom Question</option>
                </select>
            </div>

            <div class="form-group" id="custom-question-group" style="display: none;">
                <label for="custom-question">Custom Question</label>
                <textarea id="custom-question" placeholder="What is this person doing in this image? Or describe the setting, including time of day, weather etc."></textarea>
            </div>

            <button id="analyze-image-btn" class="action-button" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 11H5a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h4m6-6h4a2 2 0 0 1 2 2v3c0 1.1-.9 2-2 2h-4m-6 0V9a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2z"/>
                </svg>
                Analyze Image
            </button>

            <div class="result-area" id="analysis-result" style="display: none;">
                <label for="analysis-text">Analysis Result</label>
                <textarea id="analysis-text" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-analysis-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
