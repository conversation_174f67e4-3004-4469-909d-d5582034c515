#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔨 Building Comfyui Helper Tools...');

// Configuration
const TOOLS_DIR = 'tools';
const SCRIPT_TEMPLATE = 'script-template.js';
const OUTPUT_SCRIPT = 'script.js';

// Helper function to read file with error handling
function readFileSync(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.warn(`⚠️  Warning: Could not read ${filePath} - ${error.message}`);
        return '';
    }
}

// Helper function to escape template literals and quotes
function escapeForTemplate(str) {
    return str
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/`/g, '\\`')    // Escape backticks
        .replace(/\${/g, '\\${') // Escape template literal expressions
        .replace(/\r\n/g, '\n')  // Normalize line endings
        .trim();
}

// Helper function to extract JavaScript function content
function extractJavaScriptInit(jsContent) {
    // Look for an IIFE or initialization function
    const iifeMatch = jsContent.match(/\(function\(\)\s*\{([\s\S]*)\}\)\(\);?/);
    if (iifeMatch) {
        // Extract the content inside the IIFE, excluding the wrapper
        const innerContent = iifeMatch[1].trim();
        // Remove any initialization calls and just return the function definitions
        return innerContent.replace(/^\s*\/\/.*$/gm, '').trim();
    }
    
    // If no IIFE found, return the content as-is but wrapped in a function
    return jsContent.trim();
}

// Scan tools directory and build tool templates
function buildToolTemplates() {
    const toolTemplates = {};
    
    if (!fs.existsSync(TOOLS_DIR)) {
        console.error(`❌ Tools directory '${TOOLS_DIR}' not found!`);
        process.exit(1);
    }
    
    const toolDirs = fs.readdirSync(TOOLS_DIR, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    
    console.log(`📁 Found ${toolDirs.length} tools: ${toolDirs.join(', ')}`);
    
    for (const toolName of toolDirs) {
        const toolDir = path.join(TOOLS_DIR, toolName);
        const htmlFile = path.join(toolDir, `${toolName}.html`);
        const cssFile = path.join(toolDir, `${toolName}.css`);
        const jsFile = path.join(toolDir, `${toolName}.js`);
        
        console.log(`🔧 Processing ${toolName}...`);
        
        // Read tool files
        const htmlContent = readFileSync(htmlFile);
        const cssContent = readFileSync(cssFile);
        const jsContent = readFileSync(jsFile);
        
        // Build tool template
        const toolTemplate = {
            html: escapeForTemplate(htmlContent),
            css: escapeForTemplate(cssContent),
            js: extractJavaScriptInit(jsContent)
        };
        
        toolTemplates[toolName] = toolTemplate;
        console.log(`✅ ${toolName} processed successfully`);
    }
    
    return toolTemplates;
}

// Generate the tool templates JavaScript code
function generateToolTemplatesCode(toolTemplates) {
    let code = 'const TOOL_TEMPLATES = {\n';
    
    for (const [toolName, template] of Object.entries(toolTemplates)) {
        code += `    '${toolName}': {\n`;
        code += `        html: \`${template.html}\`,\n`;
        code += `        css: \`${template.css}\`,\n`;
        code += `        init: function() {\n`;
        code += `            ${template.js}\n`;
        code += `        }\n`;
        code += `    },\n\n`;
    }
    
    code += '};\n';
    return code;
}

// Main build function
function build() {
    try {
        // Check if template exists
        if (!fs.existsSync(SCRIPT_TEMPLATE)) {
            console.error(`❌ Script template '${SCRIPT_TEMPLATE}' not found!`);
            process.exit(1);
        }
        
        // Build tool templates
        const toolTemplates = buildToolTemplates();
        
        // Read script template
        const scriptTemplate = readFileSync(SCRIPT_TEMPLATE);
        
        // Generate tool templates code
        const toolTemplatesCode = generateToolTemplatesCode(toolTemplates);
        
        // Replace placeholder in template
        const finalScript = scriptTemplate.replace('{{TOOL_TEMPLATES}}', toolTemplatesCode);
        
        // Write output script
        fs.writeFileSync(OUTPUT_SCRIPT, finalScript, 'utf8');
        
        console.log(`🎉 Build complete! Generated ${OUTPUT_SCRIPT}`);
        console.log(`📊 Embedded ${Object.keys(toolTemplates).length} tools`);
        
    } catch (error) {
        console.error(`❌ Build failed: ${error.message}`);
        process.exit(1);
    }
}

// Run build
build();
