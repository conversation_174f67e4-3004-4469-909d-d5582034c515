// Wait for the DOM to be fully loaded before running scripts
document.addEventListener('DOMContentLoaded', function() {
    
    // --- Initialize Dashboard ---
    initializeTheme();
    initializeNavigation();
    
    // Load the default tool (prompt-generator)
    loadTool('prompt-generator');

});

// --- MODULAR DASHBOARD NAVIGATION LOGIC ---
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const targetTool = this.getAttribute('data-target');
            
            // Update active navigation state
            navLinks.forEach(navLink => navLink.parentElement.classList.remove('active'));
            this.parentElement.classList.add('active');
            
            // Load the selected tool
            loadTool(targetTool);
        });
    });
}

// --- TOOL TEMPLATES (Auto-generated from individual tool files) ---
const TOOL_TEMPLATES = {
    'aspect-ratio-calculator': {
        html: `<div class="dashboard-view active">
    <h2>Aspect Ratio Calculator</h2>
    <p class="view-description">Calculate dimensions using presets or find the ratio of custom sizes.</p>

    <div class="tool-section">
        <h3>Preset Calculator</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="aspect-ratio-select">Aspect Ratio</label>
                <select id="aspect-ratio-select">
                    <option value="1/1">1:1 (Square)</option>
                    <option value="4/3">4:3 (Standard)</option>
                    <option value="3/2">3:2 (Photography)</option>
                    <option value="16/9" selected>16:9 (Widescreen)</option>
                    <option value="9/16">9:16 (Vertical Video)</option>
                    <option value="21/9">21:9 (Ultrawide)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="preset-width">Width</label>
                <input type="number" id="preset-width" placeholder="e.g., 1920">
            </div>
            <div class="form-group">
                <label for="preset-height">Height</label>
                <input type="number" id="preset-height" placeholder="e.g., 1080">
            </div>
        </div>
    </div>

    <div class="tool-section">
        <h3>Custom Ratio Finder</h3>
        <div class="calculator-grid">
            <div class="form-group">
                <label for="custom-width">Width</label>
                <input type="number" id="custom-width" placeholder="Enter width">
            </div>
            <div class="form-group">
                <label for="custom-height">Height</label>
                <input type="number" id="custom-height" placeholder="Enter height">
            </div>
             <div class="form-group button-group">
                <button id="calculate-custom-ratio" class="action-button">Calculate Ratio</button>
            </div>
        </div>
        <div id="custom-ratio-result" class="result-box">
            Your calculated ratio will appear here.
        </div>
    </div>
</div>`,
        css: `/* --- Aspect Ratio Calculator Styles --- */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.form-group.button-group {
    justify-content: flex-end;
}

input[type="number"], select {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

input[type="number"]:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.result-box {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: var(--subtle-text-color);
    transition: all 0.3s;
}

.result-box.success {
    color: var(--success-color);
    font-weight: 500;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .calculator-grid {
        grid-template-columns: 1fr;
    }
}`,
        init: function() {
            initializeAspectRatioCalculator();

    function initializeAspectRatioCalculator() {

        const select = document.getElementById('aspect-ratio-select');
        const presetWidthInput = document.getElementById('preset-width');
        const presetHeightInput = document.getElementById('preset-height');

        const customWidthInput = document.getElementById('custom-width');
        const customHeightInput = document.getElementById('custom-height');
        const calculateBtn = document.getElementById('calculate-custom-ratio');
        const resultBox = document.getElementById('custom-ratio-result');

        select?.addEventListener('change', () => handlePresetCalculation('width'));
        presetWidthInput?.addEventListener('input', () => handlePresetCalculation('width'));
        presetHeightInput?.addEventListener('input', () => handlePresetCalculation('height'));

        calculateBtn?.addEventListener('click', calculateCustomRatio);


        function handlePresetCalculation(changedInput) {
            const ratio = select.value.split('/').map(Number); // e.g., [16, 9]
            const width = parseFloat(presetWidthInput.value);
            const height = parseFloat(presetHeightInput.value);

            if (changedInput === 'width' && width > 0) {
                const newHeight = (width / ratio[0]) * ratio[1];
                presetHeightInput.value = Math.round(newHeight);
            } else if (changedInput === 'height' && height > 0) {
                const newWidth = (height / ratio[1]) * ratio[0];
                presetWidthInput.value = Math.round(newWidth);
            }
        }

        function calculateCustomRatio() {
            const width = parseInt(customWidthInput.value);
            const height = parseInt(customHeightInput.value);

            if (!width || !height || width <= 0 || height <= 0) {
                resultBox.textContent = "Please enter valid width and height.";
                resultBox.classList.remove('success');
                return;
            }

            const divisor = gcd(width, height);
            const simplifiedWidth = width / divisor;
            const simplifiedHeight = height / divisor;
            const decimalRatio = (width / height).toFixed(3);

            resultBox.innerHTML = `Simplified: <strong>${simplifiedWidth}:${simplifiedHeight}</strong> (Decimal: ${decimalRatio})`;
            resultBox.classList.add('success');
        }

        function gcd(a, b) {
            while (b) {
                [a, b] = [b, a % b];
            }
            return a;
        }
    }
        }
    },

    'prompt-generator': {
        html: `<div class="dashboard-view active">
    <h2>Prompt Generator (For Inspire-pack)</h2>
    <p class="view-description">Automated format generator specifically for Inspire-pack. Separate prompts with a blank line to generate your formatted text file.</p>
    <div class="prompt-box-wrapper">
        <div class="prompt-box">
            <label for="positive-prompts">✅ Positive Prompts</label>
            <textarea id="positive-prompts" placeholder="A stunning portrait...&#10;&#10;A beautiful landscape..."></textarea>
        </div>
        <div class="prompt-box">
            <label for="negative-prompts">❌ Negative Prompts</label>
            <textarea id="negative-prompts" placeholder="blurry, ugly..."></textarea>
        </div>
    </div>
    <div class="options-container">
        <label class="toggle-switch">
            <input type="checkbox" id="apply-all-negative">
            <span class="slider"></span>
        </label>
        <label for="apply-all-negative">Use the first negative prompt for all</label>
    </div>
    <button id="export-button" class="action-button">Export to .txt</button>
</div>`,
        css: `/* --- Prompt Generator Styles --- */
.prompt-box-wrapper {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.prompt-box {
    flex: 1;
}

.prompt-box label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 1rem;
}

textarea {
    width: 100%;
    height: 350px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    resize: vertical;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s;
}

textarea::placeholder {
    color: var(--subtle-text-color);
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.options-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 8px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--subtle-text-color);
    transition: .4s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(22px);
}

.options-container label {
    margin-left: 12px;
    cursor: pointer;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .prompt-box-wrapper {
        flex-direction: column;
    }
}`,
        init: function() {
            initializePromptGenerator();

    function initializePromptGenerator() {
        const exportButton = document.getElementById('export-button');
        if (exportButton) {
            exportButton.addEventListener('click', exportPrompts);
        }
    }

    function exportPrompts() {
        const positiveText = document.getElementById('positive-prompts').value.trim();
        const negativeText = document.getElementById('negative-prompts').value.trim();
        const applyAllNegative = document.getElementById('apply-all-negative').checked;

        if (!positiveText) {
            alert('Please enter at least one positive prompt.');
            return;
        }

        const positivePrompts = positiveText.split(/\n\s*\n/).filter(prompt => prompt.trim());
        const negativePrompts = negativeText.split(/\n\s*\n/).filter(prompt => prompt.trim());

        let output = '';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

        positivePrompts.forEach((positivePrompt, index) => {
            const cleanPositive = positivePrompt.replace(/\n/g, ' ').trim();

            let negativePrompt = '';
            if (applyAllNegative && negativePrompts.length > 0) {

                negativePrompt = negativePrompts[0].replace(/\n/g, ' ').trim();
            } else if (negativePrompts[index]) {

                negativePrompt = negativePrompts[index].replace(/\n/g, ' ').trim();
            }

            output += `positive:${cleanPositive}\n\n`;
            output += `negative:${negativePrompt}\n`;

            if (index < positivePrompts.length - 1) {
                output += '-----------------\n';
            }
        });

        const blob = new Blob([output], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `generate.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        const originalText = exportButton.textContent;
        exportButton.textContent = '✓ Exported!';
        exportButton.style.backgroundColor = 'var(--success-color)';
        
        setTimeout(() => {
            exportButton.textContent = originalText;
            exportButton.style.backgroundColor = '';
        }, 2000);
    }
        }
    },

    'ai-image-prompt-generator': {
        html: `<div class="dashboard-view active">
    <h2>AI Image Prompt Generation</h2>
    <p class="view-description">Generate, enhance, and analyze image prompts using AI. Upload images to create prompts or enhance your existing text.</p>

    <!-- Feature Tabs -->
    <div class="feature-tabs">
        <button class="tab-button active" data-tab="image-to-prompt">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
            </svg>
            Image to Prompt
        </button>
        <button class="tab-button" data-tab="magic-enhance">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Magic Enhance
        </button>
        <button class="tab-button" data-tab="ai-describe">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 11H5a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h4m6-6h4a2 2 0 0 1 2 2v3c0 1.1-.9 2-2 2h-4m-6 0V9a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2z"/>
            </svg>
            AI Describe
        </button>
    </div>

    <!-- Image to Prompt Tab -->
    <div class="tab-content active" id="image-to-prompt">
        <div class="tool-section">
            <h3>Convert Image to Prompt</h3>
            <p class="section-description">Upload an image and select the prompt model to generate optimized prompts for AI image generation.</p>

            <div class="upload-area" id="image-upload-area">
                <div class="upload-placeholder">
                    <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    <p>Click to upload or drag and drop an image</p>
                    <p class="upload-hint">Supports JPG, PNG, WebP, GIF</p>
                </div>
                <input type="file" id="image-file-input" accept="image/*" hidden>
                <div class="uploaded-image" id="uploaded-image" style="display: none;">
                    <img id="preview-image" src="" alt="Uploaded image">
                    <button class="remove-image" id="remove-image">×</button>
                </div>
            </div>

            <div class="form-group">
                <label for="prompt-model-select">Prompt Model</label>
                <select id="prompt-model-select">
                    <option value="general">General Image Prompt - Natural language description</option>
                    <option value="flux">Flux - Optimized for Flux AI models, concise natural language</option>
                    <option value="stable-diffusion">Stable Diffusion - Formatted for Stable Diffusion models</option>
                </select>
            </div>

            <button id="generate-prompt-btn" class="action-button" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Generate Prompt
            </button>

            <div class="result-area" id="prompt-result" style="display: none;">
                <label for="generated-prompt">Generated Prompt</label>
                <textarea id="generated-prompt" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-prompt-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Magic Enhance Tab -->
    <div class="tab-content" id="magic-enhance">
        <div class="tool-section">
            <h3>Magic Enhance</h3>
            <p class="section-description">Transform simple text into detailed, descriptive image prompts perfect for AI generation.</p>

            <div class="form-group">
                <label for="simple-text">Simple Text</label>
                <textarea id="simple-text" placeholder="Enter simple text like 'a cat in a garden' or 'futuristic city'..."></textarea>
            </div>

            <button id="enhance-text-btn" class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Enhance Text
            </button>

            <div class="result-area" id="enhanced-result" style="display: none;">
                <label for="enhanced-prompt">Enhanced Prompt</label>
                <textarea id="enhanced-prompt" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-enhanced-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Describe Tab -->
    <div class="tab-content" id="ai-describe">
        <div class="tool-section">
            <h3>AI Describe</h3>
            <p class="section-description">Let AI analyze and understand your images in detail. Get descriptions, recognize objects, or ask custom questions.</p>

            <div class="upload-area" id="describe-upload-area">
                <div class="upload-placeholder">
                    <svg class="upload-icon" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    <p>Click to upload or drag and drop an image</p>
                    <p class="upload-hint">Supports JPG, PNG, WebP, GIF</p>
                </div>
                <input type="file" id="describe-file-input" accept="image/*" hidden>
                <div class="uploaded-image" id="describe-uploaded-image" style="display: none;">
                    <img id="describe-preview-image" src="" alt="Uploaded image">
                    <button class="remove-image" id="describe-remove-image">×</button>
                </div>
            </div>

            <div class="form-group">
                <label for="description-option">Description Option</label>
                <select id="description-option">
                    <option value="describe-detail">Describe Image In Detail</option>
                    <option value="describe-brief">Describe Image Briefly</option>
                    <option value="describe-person">Describe The Person</option>
                    <option value="recognize-objects">Recognize Objects</option>
                    <option value="analyze-art-style">Analyze Art Style</option>
                    <option value="extract-text">Extract Text From Image</option>
                    <option value="general-prompt">General Image Prompt</option>
                    <option value="flux-prompt">Flux Prompt</option>
                    <option value="stable-diffusion-prompt">Stable Diffusion Prompt</option>
                    <option value="custom-question">Custom Question</option>
                </select>
            </div>

            <div class="form-group" id="custom-question-group" style="display: none;">
                <label for="custom-question">Custom Question</label>
                <textarea id="custom-question" placeholder="What is this person doing in this image? Or describe the setting, including time of day, weather etc."></textarea>
            </div>

            <button id="analyze-image-btn" class="action-button" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 11H5a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h4m6-6h4a2 2 0 0 1 2 2v3c0 1.1-.9 2-2 2h-4m-6 0V9a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2z"/>
                </svg>
                Analyze Image
            </button>

            <div class="result-area" id="analysis-result" style="display: none;">
                <label for="analysis-text">Analysis Result</label>
                <textarea id="analysis-text" readonly></textarea>
                <div class="result-actions">
                    <button id="copy-analysis-btn" class="action-button secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>`,
        css: `/* --- AI Image Prompt Generator Styles --- */

/* Feature Tabs */
.feature-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--subtle-text-color);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-family: 'Poppins', sans-serif;
    font-size: 0.95rem;
    font-weight: 500;
}

.tab-button:hover {
    color: var(--text-color);
    background-color: rgba(122, 162, 247, 0.05);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-icon {
    width: 20px;
    height: 20px;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Tool Section */
.tool-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.tool-section h3 {
    margin-bottom: 0.5rem;
    color: #fff;
    font-size: 1.2rem;
}

.section-description {
    color: var(--subtle-text-color);
    margin-bottom: 2rem;
    line-height: 1.5;
}

/* Upload Area */
.upload-area {
    position: relative;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(122, 162, 247, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(122, 162, 247, 0.1);
}

.upload-placeholder {
    pointer-events: none;
}

.upload-icon {
    color: var(--subtle-text-color);
    margin-bottom: 1rem;
}

.upload-area p {
    margin: 0.5rem 0;
    color: var(--text-color);
}

.upload-hint {
    font-size: 0.9rem;
    color: var(--subtle-text-color) !important;
}

/* Uploaded Image */
.uploaded-image {
    position: relative;
    display: inline-block;
}

.uploaded-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--error-color);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.remove-image:hover {
    background-color: #d32f2f;
    transform: scale(1.1);
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.form-group textarea::placeholder {
    color: var(--subtle-text-color);
}

/* Result Area */
.result-area {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.result-area label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.result-area textarea {
    width: 100%;
    min-height: 150px;
    padding: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    resize: vertical;
    line-height: 1.5;
}

.result-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-tabs {
        flex-direction: column;
        gap: 0;
    }

    .tab-button {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }

    .tab-button.active {
        border-bottom-color: var(--primary-color);
    }

    .tool-section {
        padding: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .result-actions {
        flex-direction: column;
    }

    .action-button {
        justify-content: center;
    }
}`,
        init: function() {
            initializeAIImagePromptGenerator();

function initializeAIImagePromptGenerator() {
    // Initialize tab functionality
    initializeTabs();

    // Initialize Image to Prompt feature
    initializeImageToPrompt();

    // Initialize Magic Enhance feature
    initializeMagicEnhance();

    // Initialize AI Describe feature
    initializeAIDescribe();

    console.log('AI Image Prompt Generator tool loaded');
}

// Tab Management
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// Image to Prompt Feature
function initializeImageToPrompt() {
    const uploadArea = document.getElementById('image-upload-area');
    const fileInput = document.getElementById('image-file-input');
    const uploadedImage = document.getElementById('uploaded-image');
    const previewImage = document.getElementById('preview-image');
    const removeImageBtn = document.getElementById('remove-image');
    const generateBtn = document.getElementById('generate-prompt-btn');
    const promptResult = document.getElementById('prompt-result');
    const generatedPrompt = document.getElementById('generated-prompt');
    const copyPromptBtn = document.getElementById('copy-prompt-btn');

    let currentImageFile = null;

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        if (!currentImageFile) {
            fileInput.click();
        }
    });

    // File input change handler
    fileInput.addEventListener('change', handleImageUpload);

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleImageDrop);

    // Remove image handler
    removeImageBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        resetImageUpload();
    });

    // Generate prompt handler
    generateBtn.addEventListener('click', generatePromptFromImage);

    // Copy prompt handler
    copyPromptBtn.addEventListener('click', () => copyToClipboard(generatedPrompt.value, copyPromptBtn));

    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file && isValidImageFile(file)) {
            displayUploadedImage(file);
        }
    }

    function handleDragOver(event) {
        event.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleDragLeave(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
    }

    function handleImageDrop(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0 && isValidImageFile(files[0])) {
            displayUploadedImage(files[0]);
        }
    }

    function displayUploadedImage(file) {
        currentImageFile = file;
        const reader = new FileReader();

        reader.onload = (e) => {
            previewImage.src = e.target.result;
            uploadedImage.style.display = 'block';
            uploadArea.querySelector('.upload-placeholder').style.display = 'none';
            generateBtn.disabled = false;
        };

        reader.readAsDataURL(file);
    }

    function resetImageUpload() {
        currentImageFile = null;
        uploadedImage.style.display = 'none';
        uploadArea.querySelector('.upload-placeholder').style.display = 'block';
        generateBtn.disabled = true;
        promptResult.style.display = 'none';
        fileInput.value = '';
    }

    async function generatePromptFromImage() {
        if (!currentImageFile) return;

        const promptModel = document.getElementById('prompt-model-select').value;

        try {
            setButtonLoading(generateBtn, true);

            // Convert image to base64
            const base64Image = await fileToBase64(currentImageFile);

            // Get AI response
            const prompt = await callAIForImageToPrompt(base64Image, promptModel);

            // Display result
            generatedPrompt.value = prompt;
            promptResult.style.display = 'block';

        } catch (error) {
            console.error('Error generating prompt:', error);
            showError('Failed to generate prompt. Please check your AI provider settings.');
        } finally {
            setButtonLoading(generateBtn, false);
        }
    }
}

// Magic Enhance Feature
function initializeMagicEnhance() {
    const simpleText = document.getElementById('simple-text');
    const enhanceBtn = document.getElementById('enhance-text-btn');
    const enhancedResult = document.getElementById('enhanced-result');
    const enhancedPrompt = document.getElementById('enhanced-prompt');
    const copyEnhancedBtn = document.getElementById('copy-enhanced-btn');

    enhanceBtn.addEventListener('click', enhanceText);
    copyEnhancedBtn.addEventListener('click', () => copyToClipboard(enhancedPrompt.value, copyEnhancedBtn));

    async function enhanceText() {
        const text = simpleText.value.trim();
        if (!text) {
            showError('Please enter some text to enhance.');
            return;
        }

        try {
            setButtonLoading(enhanceBtn, true);

            const enhancedText = await callAIForTextEnhancement(text);

            enhancedPrompt.value = enhancedText;
            enhancedResult.style.display = 'block';

        } catch (error) {
            console.error('Error enhancing text:', error);
            showError('Failed to enhance text. Please check your AI provider settings.');
        } finally {
            setButtonLoading(enhanceBtn, false);
        }
    }
}

// AI Describe Feature
function initializeAIDescribe() {
    const uploadArea = document.getElementById('describe-upload-area');
    const fileInput = document.getElementById('describe-file-input');
    const uploadedImage = document.getElementById('describe-uploaded-image');
    const previewImage = document.getElementById('describe-preview-image');
    const removeImageBtn = document.getElementById('describe-remove-image');
    const descriptionOption = document.getElementById('description-option');
    const customQuestionGroup = document.getElementById('custom-question-group');
    const customQuestion = document.getElementById('custom-question');
    const analyzeBtn = document.getElementById('analyze-image-btn');
    const analysisResult = document.getElementById('analysis-result');
    const analysisText = document.getElementById('analysis-text');
    const copyAnalysisBtn = document.getElementById('copy-analysis-btn');

    let currentDescribeImageFile = null;

    // Description option change handler
    descriptionOption.addEventListener('change', () => {
        if (descriptionOption.value === 'custom-question') {
            customQuestionGroup.style.display = 'block';
        } else {
            customQuestionGroup.style.display = 'none';
        }
    });

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        if (!currentDescribeImageFile) {
            fileInput.click();
        }
    });

    // File input change handler
    fileInput.addEventListener('change', handleDescribeImageUpload);

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', handleDescribeDragOver);
    uploadArea.addEventListener('dragleave', handleDescribeDragLeave);
    uploadArea.addEventListener('drop', handleDescribeImageDrop);

    // Remove image handler
    removeImageBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        resetDescribeImageUpload();
    });

    // Analyze image handler
    analyzeBtn.addEventListener('click', analyzeImage);

    // Copy analysis handler
    copyAnalysisBtn.addEventListener('click', () => copyToClipboard(analysisText.value, copyAnalysisBtn));

    function handleDescribeImageUpload(event) {
        const file = event.target.files[0];
        if (file && isValidImageFile(file)) {
            displayDescribeUploadedImage(file);
        }
    }

    function handleDescribeDragOver(event) {
        event.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleDescribeDragLeave(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');
    }

    function handleDescribeImageDrop(event) {
        event.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0 && isValidImageFile(files[0])) {
            displayDescribeUploadedImage(files[0]);
        }
    }

    function displayDescribeUploadedImage(file) {
        currentDescribeImageFile = file;
        const reader = new FileReader();

        reader.onload = (e) => {
            previewImage.src = e.target.result;
            uploadedImage.style.display = 'block';
            uploadArea.querySelector('.upload-placeholder').style.display = 'none';
            analyzeBtn.disabled = false;
        };

        reader.readAsDataURL(file);
    }

    function resetDescribeImageUpload() {
        currentDescribeImageFile = null;
        uploadedImage.style.display = 'none';
        uploadArea.querySelector('.upload-placeholder').style.display = 'block';
        analyzeBtn.disabled = true;
        analysisResult.style.display = 'none';
        fileInput.value = '';
    }

    async function analyzeImage() {
        if (!currentDescribeImageFile) return;

        const option = descriptionOption.value;
        const question = customQuestion.value.trim();

        if (option === 'custom-question' && !question) {
            showError('Please enter a custom question.');
            return;
        }

        try {
            setButtonLoading(analyzeBtn, true);

            // Convert image to base64
            const base64Image = await fileToBase64(currentDescribeImageFile);

            // Get AI response
            const analysis = await callAIForImageAnalysis(base64Image, option, question);

            // Display result
            analysisText.value = analysis;
            analysisResult.style.display = 'block';

        } catch (error) {
            console.error('Error analyzing image:', error);
            showError('Failed to analyze image. Please check your AI provider settings.');
        } finally {
            setButtonLoading(analyzeBtn, false);
        }
    }
}

// Utility Functions
function isValidImageFile(file) {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    return validTypes.includes(file.type);
}

function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(() => {
        const originalText = button.textContent;
        button.textContent = '✓ Copied!';
        button.style.backgroundColor = 'var(--success-color)';

        setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = '';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        showError('Failed to copy to clipboard');
    });
}

function showError(message) {
    // Create or update error notification
    let errorNotification = document.querySelector('.error-notification');
    if (!errorNotification) {
        errorNotification = document.createElement('div');
        errorNotification.className = 'error-notification';
        errorNotification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--error-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            max-width: 400px;
        `;
        document.body.appendChild(errorNotification);
    }

    errorNotification.textContent = message;

    // Show notification
    requestAnimationFrame(() => {
        errorNotification.style.opacity = '1';
        errorNotification.style.transform = 'translateY(0)';
    });

    // Hide notification after 5 seconds
    setTimeout(() => {
        errorNotification.style.opacity = '0';
        errorNotification.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            if (errorNotification.parentNode) {
                errorNotification.parentNode.removeChild(errorNotification);
            }
        }, 300);
    }, 5000);
}

// AI API Functions (placeholder implementations)
async function callAIForImageToPrompt(base64Image, promptModel) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

    const prompts = {
        'general': 'A detailed natural language description of the image showing [placeholder - AI provider not configured]',
        'flux': 'Concise, natural language prompt optimized for Flux: [placeholder - AI provider not configured]',
        'stable-diffusion': 'Structured prompt with tags for Stable Diffusion: [placeholder - AI provider not configured]'
    };

    return prompts[promptModel] || prompts['general'];
}

async function callAIForTextEnhancement(text) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call

    return `Enhanced version of "${text}": [placeholder - AI provider not configured. Please configure your AI provider in Settings to use this feature.]`;
}

async function callAIForImageAnalysis(base64Image, option, customQuestion) {
    // This will be implemented when we add the LLM provider settings
    // For now, return a placeholder response
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

    const responses = {
        'describe-detail': 'Detailed image description: [placeholder - AI provider not configured]',
        'describe-brief': 'Brief image description: [placeholder - AI provider not configured]',
        'describe-person': 'Person description: [placeholder - AI provider not configured]',
        'recognize-objects': 'Objects recognized: [placeholder - AI provider not configured]',
        'analyze-art-style': 'Art style analysis: [placeholder - AI provider not configured]',
        'extract-text': 'Extracted text: [placeholder - AI provider not configured]',
        'general-prompt': 'General image prompt: [placeholder - AI provider not configured]',
        'flux-prompt': 'Flux prompt: [placeholder - AI provider not configured]',
        'stable-diffusion-prompt': 'Stable Diffusion prompt: [placeholder - AI provider not configured]',
        'custom-question': `Answer to "${customQuestion}": [placeholder - AI provider not configured]`
    };

    return responses[option] || 'Analysis result: [placeholder - AI provider not configured]';
}
        }
    },

    'settings': {
        html: `<div class="dashboard-view active">
    <h2>Settings</h2>
    <p class="view-description">Configure your dashboard preferences and tool settings.</p>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
            Appearance
        </h3>
        <div class="setting-group">
            <label for="theme-select" class="setting-label">Theme</label>
            <p class="setting-description">Choose your preferred color theme for the dashboard</p>
            <select id="theme-select" class="setting-select">
                <option value="dark">Dark (Default)</option>
                <option value="light">Light</option>
                <option value="ocean">Ocean</option>
                <option value="forest">Forest</option>
                <option value="purple">Purple</option>
            </select>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Preferences
        </h3>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="auto-save-settings">
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Auto-save settings</span>
                    <span class="checkbox-description">Automatically save your preferences</span>
                </div>
            </label>
        </div>
        <div class="setting-group">
            <label class="setting-checkbox">
                <input type="checkbox" id="show-tooltips" checked>
                <span class="checkmark"></span>
                <div class="checkbox-content">
                    <span class="checkbox-label">Show tooltips</span>
                    <span class="checkbox-description">Display helpful tooltips throughout the interface</span>
                </div>
            </label>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 19c-5 0-8-3-8-6s4-6 9-7c5-1 12 0 12 6-1 0-4 1-4 4s3 4 4 4c-1 6-7 6-12 6-2 0-3-1-1-3"/>
            </svg>
            AI LLM Providers
        </h3>
        <div class="setting-group">
            <label for="llm-provider-select" class="setting-label">AI Provider</label>
            <p class="setting-description">Choose your preferred AI provider for image analysis and prompt generation</p>
            <select id="llm-provider-select" class="setting-select">
                <option value="">Select AI Provider</option>
                <optgroup label="Cloud Providers">
                    <option value="openai">OpenAI (GPT-4 Vision)</option>
                    <option value="gemini">Google Gemini</option>
                    <option value="groq">Groq</option>
                </optgroup>
                <optgroup label="Local Providers">
                    <option value="ollama">Ollama</option>
                    <option value="lmstudio">LM Studio</option>
                </optgroup>
            </select>
        </div>

        <div class="setting-group" id="api-key-group" style="display: none;">
            <label for="api-key-input" class="setting-label">API Key</label>
            <p class="setting-description">Enter your API key for the selected provider</p>
            <div class="api-key-container">
                <input type="password" id="api-key-input" class="setting-input" placeholder="Enter your API key">
                <button type="button" id="toggle-api-key" class="toggle-password-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="setting-group" id="local-url-group" style="display: none;">
            <label for="local-url-input" class="setting-label">Local Server URL</label>
            <p class="setting-description">Enter the URL for your local AI server</p>
            <input type="url" id="local-url-input" class="setting-input" placeholder="http://localhost:11434">
        </div>

        <div class="setting-group" id="model-name-group" style="display: none;">
            <label for="model-name-input" class="setting-label">Model Name</label>
            <p class="setting-description">Enter the specific model name to use</p>
            <input type="text" id="model-name-input" class="setting-input" placeholder="e.g., llava, gpt-4-vision-preview">
        </div>

        <div class="setting-group">
            <button id="test-connection" class="action-button secondary" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                    <polyline points="22,4 12,14.01 9,11.01"/>
                </svg>
                Test Connection
            </button>
        </div>
    </div>

    <div class="settings-section">
        <h3>
            <svg class="section-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
            </svg>
            Actions
        </h3>
        <div class="setting-group">
            <button id="reset-settings" class="action-button secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 4v6h6"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset to Defaults
            </button>
        </div>
    </div>

    <div class="settings-footer">
        <p class="settings-info">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 16v-4"/>
                <path d="M12 8h.01"/>
            </svg>
            Settings are automatically saved to your browser's local storage
        </p>
    </div>
</div>`,
        css: `/* Settings Tool Styles */
.settings-section {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.settings-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    font-size: 1.1rem;
}

.section-icon {
    color: var(--primary-color);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.setting-select {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
    cursor: pointer;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

/* Custom Checkbox Styles */
.setting-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.setting-checkbox:hover {
    background-color: rgba(122, 162, 247, 0.05);
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s;
    flex-shrink: 0;
    margin-top: 2px;
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-content {
    flex: 1;
}

.checkbox-label {
    display: block;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.checkbox-description {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    line-height: 1.4;
}

/* Secondary Action Button */
.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    width: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
}

.action-button.secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Settings Footer */
.settings-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.settings-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--subtle-text-color);
    margin: 0;
}

.settings-info svg {
    color: var(--primary-color);
    flex-shrink: 0;
}

/* LLM Provider Settings Styles */
.setting-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s;
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(122, 162, 247, 0.2);
}

.api-key-container {
    position: relative;
    display: flex;
    align-items: center;
}

.api-key-container input {
    padding-right: 3rem;
}

.toggle-password-btn {
    position: absolute;
    right: 0.75rem;
    background: none;
    border: none;
    color: var(--subtle-text-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s;
}

.toggle-password-btn:hover {
    color: var(--text-color);
    background-color: rgba(122, 162, 247, 0.1);
}

.connection-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
}

.connection-status.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.connection-status.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-section {
        padding: 1.5rem;
    }
    
    .setting-checkbox {
        padding: 0.5rem;
    }
}`,
        init: function() {
            initializeSettings();

    function initializeSettings() {

        loadSettings();

        setupEventListeners();
        
        console.log('Settings tool loaded');
    }

    function setupEventListeners() {

        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', handleThemeChange);
        }

        const autoSaveCheckbox = document.getElementById('auto-save-settings');
        if (autoSaveCheckbox) {
            autoSaveCheckbox.addEventListener('change', handleAutoSaveChange);
        }

        const tooltipsCheckbox = document.getElementById('show-tooltips');
        if (tooltipsCheckbox) {
            tooltipsCheckbox.addEventListener('change', handleTooltipsChange);
        }

        const resetButton = document.getElementById('reset-settings');
        if (resetButton) {
            resetButton.addEventListener('click', handleResetSettings);
        }

        // LLM Provider settings
        const llmProviderSelect = document.getElementById('llm-provider-select');
        if (llmProviderSelect) {
            llmProviderSelect.addEventListener('change', handleLLMProviderChange);
        }

        const apiKeyInput = document.getElementById('api-key-input');
        if (apiKeyInput) {
            apiKeyInput.addEventListener('input', handleLLMSettingsChange);
        }

        const localUrlInput = document.getElementById('local-url-input');
        if (localUrlInput) {
            localUrlInput.addEventListener('input', handleLLMSettingsChange);
        }

        const modelNameInput = document.getElementById('model-name-input');
        if (modelNameInput) {
            modelNameInput.addEventListener('input', handleLLMSettingsChange);
        }

        const toggleApiKeyBtn = document.getElementById('toggle-api-key');
        if (toggleApiKeyBtn) {
            toggleApiKeyBtn.addEventListener('click', toggleApiKeyVisibility);
        }

        const testConnectionBtn = document.getElementById('test-connection');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', testLLMConnection);
        }
    }

    function handleThemeChange(event) {
        const selectedTheme = event.target.value;
        applyTheme(selectedTheme);
        saveSettings();

        showSettingsFeedback('Theme changed successfully!');
    }

    function applyTheme(theme) {

        if (theme === 'dark') {
            document.documentElement.removeAttribute('data-theme');
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }
    }

    function handleAutoSaveChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Auto-save ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleTooltipsChange(event) {
        const isEnabled = event.target.checked;
        saveSettings();
        showSettingsFeedback(`Tooltips ${isEnabled ? 'enabled' : 'disabled'}`);
    }

    function handleResetSettings() {
        if (confirm('Are you sure you want to reset all settings to their defaults? This action cannot be undone.')) {

            const defaults = {
                theme: 'dark',
                autoSave: false,
                showTooltips: true
            };

            document.getElementById('theme-select').value = defaults.theme;
            document.getElementById('auto-save-settings').checked = defaults.autoSave;
            document.getElementById('show-tooltips').checked = defaults.showTooltips;

            applyTheme(defaults.theme);

            localStorage.setItem('comfyui-helper-settings', JSON.stringify(defaults));
            
            showSettingsFeedback('Settings reset to defaults');
        }
    }

    // LLM Provider Handler Functions
    function handleLLMProviderChange(event) {
        const provider = event.target.value;
        const apiKeyGroup = document.getElementById('api-key-group');
        const localUrlGroup = document.getElementById('local-url-group');
        const modelNameGroup = document.getElementById('model-name-group');
        const testConnectionBtn = document.getElementById('test-connection');

        // Hide all groups initially
        apiKeyGroup.style.display = 'none';
        localUrlGroup.style.display = 'none';
        modelNameGroup.style.display = 'none';
        testConnectionBtn.disabled = true;

        // Show relevant groups based on provider
        if (provider === 'openai' || provider === 'gemini' || provider === 'groq') {
            apiKeyGroup.style.display = 'block';
            modelNameGroup.style.display = 'block';
            testConnectionBtn.disabled = false;
        } else if (provider === 'ollama' || provider === 'lmstudio') {
            localUrlGroup.style.display = 'block';
            modelNameGroup.style.display = 'block';
            testConnectionBtn.disabled = false;
        }

        // Set default values
        const localUrlInput = document.getElementById('local-url-input');
        const modelNameInput = document.getElementById('model-name-input');

        if (provider === 'ollama') {
            localUrlInput.value = 'http://localhost:11434';
            modelNameInput.placeholder = 'e.g., llava, llava:13b';
        } else if (provider === 'lmstudio') {
            localUrlInput.value = 'http://localhost:1234';
            modelNameInput.placeholder = 'e.g., llava-v1.6-mistral-7b';
        } else if (provider === 'openai') {
            modelNameInput.placeholder = 'gpt-4-vision-preview';
        } else if (provider === 'gemini') {
            modelNameInput.placeholder = 'gemini-pro-vision';
        } else if (provider === 'groq') {
            modelNameInput.placeholder = 'llava-v1.5-7b-4096-preview';
        }

        saveLLMSettings();
        showSettingsFeedback('AI provider updated');
    }

    function handleLLMSettingsChange() {
        saveLLMSettings();
    }

    function toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('api-key-input');
        const toggleBtn = document.getElementById('toggle-api-key');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            toggleBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
            `;
        } else {
            apiKeyInput.type = 'password';
            toggleBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
            `;
        }
    }

    async function testLLMConnection() {
        const testBtn = document.getElementById('test-connection');
        const originalText = testBtn.textContent;

        // Remove any existing status
        const existingStatus = document.querySelector('.connection-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        try {
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';

            const settings = getLLMSettings();
            if (!settings.provider) {
                throw new Error('Please select an AI provider');
            }

            // Simulate connection test (replace with actual API call)
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Show success status
            const statusDiv = document.createElement('div');
            statusDiv.className = 'connection-status success';
            statusDiv.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20,6 9,17 4,12"/>
                </svg>
                Connection successful!
            `;
            testBtn.parentNode.appendChild(statusDiv);

        } catch (error) {
            // Show error status
            const statusDiv = document.createElement('div');
            statusDiv.className = 'connection-status error';
            statusDiv.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                ${error.message}
            `;
            testBtn.parentNode.appendChild(statusDiv);
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
    }

    function saveSettings() {
        const settings = {
            theme: document.getElementById('theme-select')?.value || 'dark',
            autoSave: document.getElementById('auto-save-settings')?.checked || false,
            showTooltips: document.getElementById('show-tooltips')?.checked || true
        };
        
        localStorage.setItem('comfyui-helper-settings', JSON.stringify(settings));
    }

    function saveLLMSettings() {
        const settings = {
            provider: document.getElementById('llm-provider-select')?.value || '',
            apiKey: document.getElementById('api-key-input')?.value || '',
            localUrl: document.getElementById('local-url-input')?.value || '',
            modelName: document.getElementById('model-name-input')?.value || ''
        };

        localStorage.setItem('comfyui-helper-llm-settings', JSON.stringify(settings));
    }

    function getLLMSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-llm-settings');
            if (savedSettings) {
                return JSON.parse(savedSettings);
            }
        } catch (error) {
            console.warn('Failed to load LLM settings:', error);
        }

        return {
            provider: '',
            apiKey: '',
            localUrl: '',
            modelName: ''
        };
    }

    function loadLLMSettings() {
        const settings = getLLMSettings();

        const providerSelect = document.getElementById('llm-provider-select');
        if (providerSelect && settings.provider) {
            providerSelect.value = settings.provider;
            // Trigger change event to show/hide relevant fields
            providerSelect.dispatchEvent(new Event('change'));
        }

        const apiKeyInput = document.getElementById('api-key-input');
        if (apiKeyInput && settings.apiKey) {
            apiKeyInput.value = settings.apiKey;
        }

        const localUrlInput = document.getElementById('local-url-input');
        if (localUrlInput && settings.localUrl) {
            localUrlInput.value = settings.localUrl;
        }

        const modelNameInput = document.getElementById('model-name-input');
        if (modelNameInput && settings.modelName) {
            modelNameInput.value = settings.modelName;
        }
    }

    function loadSettings() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                if (settings.theme) {
                    const themeSelect = document.getElementById('theme-select');
                    if (themeSelect) {
                        themeSelect.value = settings.theme;
                        applyTheme(settings.theme);
                    }
                }

                const autoSaveCheckbox = document.getElementById('auto-save-settings');
                if (autoSaveCheckbox && typeof settings.autoSave === 'boolean') {
                    autoSaveCheckbox.checked = settings.autoSave;
                }
                
                const tooltipsCheckbox = document.getElementById('show-tooltips');
                if (tooltipsCheckbox && typeof settings.showTooltips === 'boolean') {
                    tooltipsCheckbox.checked = settings.showTooltips;
                }
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }

        // Load LLM settings
        loadLLMSettings();
    }

    function showSettingsFeedback(message) {

        let feedback = document.querySelector('.settings-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'settings-feedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--success-color);
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }
        
        feedback.textContent = message;

        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
            feedback.style.transform = 'translateY(0)';
        });

        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    function initializeThemeOnLoad() {
        try {
            const savedSettings = localStorage.getItem('comfyui-helper-settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                if (settings.theme) {
                    applyTheme(settings.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to initialize theme:', error);
        }
    }

    initializeThemeOnLoad();
        }
    },

};


// --- TOOL LOADING SYSTEM (No Server Required) ---
async function loadTool(toolName) {
    const contentArea = document.getElementById('tool-content');
    
    try {
        // Show loading state
        contentArea.innerHTML = '<div class="loading">Loading...</div>';
        
        // Get tool template
        const toolTemplate = TOOL_TEMPLATES[toolName];
        if (!toolTemplate) {
            throw new Error(`Tool ${toolName} not found`);
        }
        
        // Load HTML content
        contentArea.innerHTML = toolTemplate.html;
        
        // Load CSS
        loadToolCSS(toolName, toolTemplate.css);
        
        // Initialize tool functionality
        if (toolTemplate.init) {
            toolTemplate.init();
        }
        
    } catch (error) {
        console.error(`Error loading tool ${toolName}:`, error);
        contentArea.innerHTML = `<div class="error">Failed to load ${toolName}. Please try again.</div>`;
    }
}

// Load tool-specific CSS
function loadToolCSS(toolName, cssContent) {
    const cssId = `${toolName}-css`;
    
    // Remove existing tool CSS
    const existingCSS = document.getElementById(cssId);
    if (existingCSS) {
        existingCSS.remove();
    }
    
    // Add new CSS
    const style = document.createElement('style');
    style.id = cssId;
    style.textContent = cssContent;
    document.head.appendChild(style);
}

// --- THEME INITIALIZATION ---
function initializeTheme() {
    try {
        const savedSettings = localStorage.getItem('comfyui-helper-settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.theme && settings.theme !== 'dark') {
                document.documentElement.setAttribute('data-theme', settings.theme);
            }
        }
    } catch (error) {
        console.warn('Failed to initialize theme:', error);
    }
}
