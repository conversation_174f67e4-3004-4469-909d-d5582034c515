// Wait for the DOM to be fully loaded before running scripts
document.addEventListener('DOMContentLoaded', function() {
    
    // --- Initialize Dashboard ---
    initializeTheme();
    initializeNavigation();
    
    // Load the default tool (prompt-generator)
    loadTool('prompt-generator');

});

// --- MODULAR DASHBOARD NAVIGATION LOGIC ---
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav a');

    navLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            const targetTool = this.getAttribute('data-target');
            
            // Update active navigation state
            navLinks.forEach(navLink => navLink.parentElement.classList.remove('active'));
            this.parentElement.classList.add('active');
            
            // Load the selected tool
            loadTool(targetTool);
        });
    });
}

// --- TOOL TEMPLATES (Auto-generated from individual tool files) ---
{{TOOL_TEMPLATES}}

// --- TOOL LOADING SYSTEM (No Server Required) ---
async function loadTool(toolName) {
    const contentArea = document.getElementById('tool-content');
    
    try {
        // Show loading state
        contentArea.innerHTML = '<div class="loading">Loading...</div>';
        
        // Get tool template
        const toolTemplate = TOOL_TEMPLATES[toolName];
        if (!toolTemplate) {
            throw new Error(`Tool ${toolName} not found`);
        }
        
        // Load HTML content
        contentArea.innerHTML = toolTemplate.html;
        
        // Load CSS
        loadToolCSS(toolName, toolTemplate.css);
        
        // Initialize tool functionality
        if (toolTemplate.init) {
            toolTemplate.init();
        }
        
    } catch (error) {
        console.error(`Error loading tool ${toolName}:`, error);
        contentArea.innerHTML = `<div class="error">Failed to load ${toolName}. Please try again.</div>`;
    }
}

// Load tool-specific CSS
function loadToolCSS(toolName, cssContent) {
    const cssId = `${toolName}-css`;
    
    // Remove existing tool CSS
    const existingCSS = document.getElementById(cssId);
    if (existingCSS) {
        existingCSS.remove();
    }
    
    // Add new CSS
    const style = document.createElement('style');
    style.id = cssId;
    style.textContent = cssContent;
    document.head.appendChild(style);
}

// --- THEME INITIALIZATION ---
function initializeTheme() {
    try {
        const savedSettings = localStorage.getItem('comfyui-helper-settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.theme && settings.theme !== 'dark') {
                document.documentElement.setAttribute('data-theme', settings.theme);
            }
        }
    } catch (error) {
        console.warn('Failed to initialize theme:', error);
    }
}
